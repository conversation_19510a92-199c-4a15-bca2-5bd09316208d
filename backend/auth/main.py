from contextlib import asynccontextmanager
import secrets
import base64
import hashlib
from urllib.parse import urlencode

from fastapi import Depends, FastAPI, Request, Security, HTTPException
from fastapi.responses import RedirectResponse
from fastapi_zitadel_auth import <PERSON><PERSON>del<PERSON><PERSON>
from fastapi_zitadel_auth.exceptions import ForbiddenException
from fastapi_zitadel_auth.user import DefaultZitadel<PERSON><PERSON>
from pydantic import HttpUrl
from starlette.middleware.cors import CORSMiddleware

from config import get_settings

settings = get_settings()
# print(settings.model_dump(mode="json"))

# Create a ZitadelAuth object usable as a FastAPI dependency
zitadel_auth = ZitadelAuth(
    issuer_url=HttpUrl(settings.ZITADEL_ISSUER_URL),
    project_id=settings.ZITADEL_PROJECT_ID,
    app_client_id=settings.ZITADEL_CLIENT_ID,
    allowed_scopes={
        "openid": "OpenID Connect",
        "email": "Email",
        "profile": "Profile",
        "urn:zitadel:iam:org:project:id:zitadel:aud": "Audience",
        "urn:zitadel:iam:org:projects:roles": "Roles",
    },
)


# Create a dependency to validate that the user has the required role
async def validate_is_admin_user(
    user: DefaultZitadelUser = Depends(zitadel_auth),
) -> None:
    required_role = "admin"
    if required_role not in user.claims.project_roles.keys():
        raise ForbiddenException(f"User does not have role assigned: {required_role}")


IsAdmin = Security(validate_is_admin_user)
Authenticated = Security(zitadel_auth, scopes=["user"])


# Load OpenID configuration at startup
@asynccontextmanager
async def lifespan(app: FastAPI):  # noqa
    await zitadel_auth.openid_config.load_config()
    yield


# Create a FastAPI app and configure Swagger UI
app = FastAPI(
    title="dsai-auth",
    lifespan=lifespan,
)

# CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# Helper function to generate PKCE parameters
def generate_pkce_params():
    """Generate PKCE code verifier and challenge for OAuth2 flow"""
    code_verifier = (
        base64.urlsafe_b64encode(secrets.token_bytes(32)).decode("utf-8").rstrip("=")
    )
    code_challenge = (
        base64.urlsafe_b64encode(hashlib.sha256(code_verifier.encode("utf-8")).digest())
        .decode("utf-8")
        .rstrip("=")
    )
    return code_verifier, code_challenge


@app.get("/login")
async def login():
    """
    Redirect to Zitadel login page using OIDC Authorization Code flow with PKCE
    """
    # Generate PKCE parameters
    _, code_challenge = generate_pkce_params()

    # Build authorization URL
    auth_params = {
        "response_type": "code",
        "client_id": settings.ZITADEL_CLIENT_ID,
        "redirect_uri": settings.REDIRECT_URI,
        "scope": "openid profile email urn:zitadel:iam:org:projects:roles",
        "state": secrets.token_urlsafe(32),
        "code_challenge": code_challenge,
        "code_challenge_method": "S256",
    }

    auth_url = (
        f"{settings.ZITADEL_ISSUER_URL}/oauth/v2/authorize?{urlencode(auth_params)}"
    )

    return RedirectResponse(url=auth_url, status_code=302)


@app.get("/register")
async def register():
    """
    Redirect to Zitadel registration page
    """
    # Generate PKCE parameters
    _, code_challenge = generate_pkce_params()

    # Build authorization URL with registration prompt
    auth_params = {
        "response_type": "code",
        "client_id": settings.ZITADEL_CLIENT_ID,
        "redirect_uri": settings.REDIRECT_URI,
        "scope": "openid profile email urn:zitadel:iam:org:projects:roles",
        "state": secrets.token_urlsafe(32),
        "code_challenge": code_challenge,
        "code_challenge_method": "S256",
        "prompt": "create",  # This tells Zitadel to show the registration form
    }

    auth_url = (
        f"{settings.ZITADEL_ISSUER_URL}/oauth/v2/authorize?{urlencode(auth_params)}"
    )

    return RedirectResponse(url=auth_url, status_code=302)


async def get_optional_user(request: Request) -> DefaultZitadelUser | None:
    """
    Optional dependency to get user if authenticated, returns None if not
    """
    try:
        # Check if Authorization header exists and has a Bearer token
        auth_header = request.headers.get("Authorization")
        if not auth_header or not auth_header.startswith("Bearer "):
            return None

        # Use the zitadel_auth dependency to validate the token
        from fastapi.security import SecurityScopes

        security_scopes = SecurityScopes()
        user = await zitadel_auth(request, security_scopes)
        return user
    except Exception:
        # If any exception occurs (no token, invalid token, etc.), user is not authenticated
        return None


@app.get("/status")
async def status(user: DefaultZitadelUser | None = Depends(get_optional_user)):
    """
    Check if the user is authenticated
    Returns true if authenticated, false otherwise
    """
    if user:
        return {"authenticated": True, "user_id": user.claims.sub}
    else:
        return {"authenticated": False}


if __name__ == "__main__":
    import uvicorn

    uvicorn.run("main:app", reload=True, port=8001)
