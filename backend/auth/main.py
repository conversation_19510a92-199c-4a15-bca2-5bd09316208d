from contextlib import asynccontextmanager

from fastapi import Depends, FastAP<PERSON>, Request, Security
from fastapi_zitadel_auth import <PERSON><PERSON><PERSON><PERSON><PERSON>
from fastapi_zitadel_auth.exceptions import ForbiddenException
from fastapi_zitadel_auth.user import Default<PERSON><PERSON>delUser
from pydantic import HttpUrl
from starlette.middleware.cors import CORSMiddleware

from config import get_settings

settings = get_settings()
# print(settings.model_dump(mode="json"))

# Create a ZitadelAuth object usable as a FastAPI dependency
zitadel_auth = ZitadelAuth(
    issuer_url=HttpUrl(settings.ZITADEL_ISSUER_URL),
    project_id=settings.ZITADEL_PROJECT_ID,
    app_client_id=settings.ZITADEL_CLIENT_ID,
    allowed_scopes={
        "openid": "OpenID Connect",
        "email": "Email",
        "profile": "Profile",
        "urn:zitadel:iam:org:project:id:zitadel:aud": "Audience",
        "urn:zitadel:iam:org:projects:roles": "Roles",
    },
)


# Create a dependency to validate that the user has the required role
async def validate_is_admin_user(
    user: DefaultZitadelUser = Depends(zitadel_auth),
) -> None:
    required_role = "admin"
    if required_role not in user.claims.project_roles.keys():
        raise ForbiddenException(f"User does not have role assigned: {required_role}")


IsAdmin = Security(validate_is_admin_user)
Authenticated = Security(zitadel_auth, scopes=["user"])


# Load OpenID configuration at startup
@asynccontextmanager
async def lifespan(app: FastAPI):  # noqa
    await zitadel_auth.openid_config.load_config()
    yield


# Create a FastAPI app and configure Swagger UI
app = FastAPI(
    title="dsai-auth",
    lifespan=lifespan,
)

# CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)



if __name__ == "__main__":
    import uvicorn

    uvicorn.run("main:app", reload=True, port=8001)
