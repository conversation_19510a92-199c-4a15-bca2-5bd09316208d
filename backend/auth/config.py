from functools import lru_cache
from pydantic import HttpUrl
from pydantic_settings import BaseSettings, SettingsConfigDict


class Config(BaseSettings):

    ZITADEL_ISSUER_URL: HttpUrl
    ZITADEL_PROJECT_ID: str
    ZITADEL_CLIENT_ID: str

    PRIVATE_KEY_PATH: str
    REDIRECT_URI: str = "http://localhost:8080/auth/callback"

    HOST: str = "localhost"
    PORT: int = 8000
    RELOAD: bool = False

    model_config = SettingsConfigDict(
        extra="allow",
        env_file=".env",
        env_file_encoding="utf-8",
    )


@lru_cache
def get_settings():
    return Config()
